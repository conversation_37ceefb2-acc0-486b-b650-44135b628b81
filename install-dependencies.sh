#!/bin/bash

# Installation script for EMS project dependencies
echo "🚀 Installing EMS Project Dependencies"
echo "======================================"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if npm is installed
if ! command_exists npm; then
    echo "❌ npm is not installed. Please install Node.js and npm first."
    exit 1
fi

echo "📦 Installing Backend Dependencies..."
cd backend

if [ ! -f "package.json" ]; then
    echo "❌ Backend package.json not found!"
    exit 1
fi

# Install backend dependencies
npm install

if [ $? -eq 0 ]; then
    echo "✅ Backend dependencies installed successfully"
else
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

# Go back to root and install frontend dependencies
cd ..
echo "📦 Installing Frontend Dependencies..."
cd frontend

if [ ! -f "package.json" ]; then
    echo "❌ Frontend package.json not found!"
    exit 1
fi

# Install frontend dependencies
npm install

if [ $? -eq 0 ]; then
    echo "✅ Frontend dependencies installed successfully"
else
    echo "❌ Failed to install frontend dependencies"
    exit 1
fi

cd ..

echo ""
echo "🎉 All dependencies installed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Copy backend/.env.example to backend/.env and configure it"
echo "2. Copy frontend/.env.example to frontend/.env and configure it (if needed)"
echo "3. Run ./start-dev.sh (Linux/Mac) or start-dev.bat (Windows) to start both servers"
echo ""
echo "📍 Default URLs:"
echo "   Frontend: http://localhost:5173"
echo "   Backend:  http://localhost:5000"
echo "   Health:   http://localhost:5000/health"
