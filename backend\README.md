# EMS Backend

Backend API server for the Employee Management System with **MongoDB as PRIMARY DATABASE**.

## Setup

1. **Install Dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Firebase Configuration (Optional)**
   - Place your `firebase-admin-sdk.json` file in the `src/config/` directory
   - This is required for push notifications

## Running the Server

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### Run User Fetch Utility
```bash
npm run fetch-users
```

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Notifications
- `POST /api/notifications/send-notifications` - Send notifications to all users
- `POST /api/notifications/send-singlenotifications` - Send notification to specific user

### Slack Integration
- `POST /api/slack/send-slack` - Send Slack notification (approval)
- `POST /api/slack/send-slackreject` - Send Slack notification (rejection)
- `POST /api/slack/send-dailylog-slack` - Send daily log to Slack
- `GET /api/slack/debug-users-slack` - Debug Slack user IDs
- `POST /api/slack/fix-slack-ids` - Fix Slack ID whitespace issues
- `POST /api/slack/test-slack-user` - Test Slack user ID

### Email
- `POST /api/email/send-email` - Send leave request email
- `POST /api/email/send-alertemail` - Send bulk alert emails
- `POST /api/email/send-response` - Send approval response
- `POST /api/email/send-rejectresponse` - Send rejection response
- `POST /api/email/sendtaskemail` - Send task assignment email
- `POST /api/email/inviteClient` - Send client invitation email

### Legacy Routes (Backward Compatibility)
- `POST /sendtaskemail` - Send task assignment email (legacy)
- `POST /inviteClient` - Send client invitation email (legacy)

### MongoDB Operations (PRIMARY DATABASE)
- `GET /api/mongodb/health` - MongoDB connection status
- `GET /api/mongodb/users` - Get all users from MongoDB
- `GET /api/mongodb/users/:id` - Get user by ID
- `POST /api/mongodb/users` - Create new user
- `PUT /api/mongodb/users/:id` - Update user
- `DELETE /api/mongodb/users/:id` - Deactivate user
- `GET /api/mongodb/attendance` - Get attendance logs
- `POST /api/mongodb/attendance` - Create attendance log
- `PUT /api/mongodb/attendance/:id` - Update attendance log
- `GET /api/mongodb/projects` - Get all projects
- `POST /api/mongodb/projects` - Create new project

### Data Synchronization (Supabase → MongoDB)
- `POST /api/sync/full-sync` - Sync all data from Supabase to MongoDB
- `POST /api/sync/sync-users` - Sync users only
- `POST /api/sync/sync-attendance` - Sync attendance logs
- `POST /api/sync/sync-projects` - Sync projects only
- `GET /api/sync/status` - Get sync service status

### PDF Generation
- `POST /api/pdf/generate-pdfDaily` - Generate daily attendance PDF
- `POST /api/pdf/generate-pdfWeekly` - Generate weekly attendance PDF
- `POST /api/pdf/generate-pdfMonthly` - Generate monthly attendance PDF
- `POST /api/pdf/generate-Filtered` - Generate filtered attendance PDF

## Project Structure

```
backend/
├── src/
│   ├── config/          # Configuration files
│   ├── controllers/     # Route controllers
│   ├── middleware/      # Express middleware
│   ├── routes/          # API routes
│   ├── utils/           # Utility functions
│   └── server.js        # Main server file
├── .env.example         # Environment variables template
├── package.json         # Dependencies and scripts
└── README.md           # This file
```

## Environment Variables

### Required Variables
```env
# Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB (Primary Database - REQUIRED)
MONGODB_URI=mongodb+srv://techcreator2019:<password>@ewbackend.wq8uaqx.mongodb.net/
MONGODB_DB_NAME=ems_database

# Supabase (Secondary Database - Optional)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key

# Slack Integration
VITE_SLACK_BOT_USER_OAUTH_TOKEN=your_slack_token
VITE_SLACK_WEBHOOK_URL=your_slack_webhook

# Email Configuration
VITE_EMAIL_USER=your_gmail_address
VITE_EMAIL_PASS=your_gmail_app_password
SENDGRID_API_KEY=your_sendgrid_key
```

See `.env.example` for the complete template.

## Dependencies

### Core Framework
- **express** - Web framework
- **cors** - Cross-origin resource sharing
- **dotenv** - Environment variable management

### Databases
- **@supabase/supabase-js** - Supabase client (primary database)
- **mongoose** - MongoDB ODM (secondary database)

### Communication & Notifications
- **firebase-admin** - Firebase Admin SDK for push notifications
- **nodemailer** - Email sending
- **@sendgrid/mail** - SendGrid email service
- **node-fetch** - HTTP requests

### Utilities
- **node-cron** - Scheduled tasks
- **puppeteer** - PDF generation with headless Chrome
- **html-pdf** - HTML to PDF conversion
- **pdfkit** - PDF generation library
- **body-parser** - Request body parsing
