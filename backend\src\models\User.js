import mongoose from 'mongoose';

const userSchema = new mongoose.Schema({
    // Basic Information
    supabase_id: {
        type: String,
        unique: true,
        sparse: true, // Allow null values but ensure uniqueness when present
        index: true
    },
    full_name: {
        type: String,
        required: true,
        trim: true
    },
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        index: true
    },
    personal_email: {
        type: String,
        lowercase: true,
        trim: true
    },
    phone_number: {
        type: String,
        trim: true
    },
    
    // Role and Organization
    role: {
        type: String,
        enum: ['admin', 'employee', 'manager', 'client', 'product manager', 'super admin'],
        default: 'employee'
    },
    organization_id: {
        type: String,
        index: true
    },
    
    // Work Information
    joining_date: {
        type: Date,
        default: Date.now
    },
    location: {
        type: String,
        trim: true
    },
    department: {
        type: String,
        trim: true
    },
    position: {
        type: String,
        trim: true
    },
    
    // Communication
    slack_id: {
        type: String,
        trim: true,
        index: true
    },
    fcm_token: {
        type: String,
        trim: true
    },
    
    // Status
    is_active: {
        type: Boolean,
        default: true
    },
    last_login: {
        type: Date
    },
    
    // Metadata
    client_metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    
    // Salary Information
    salary: {
        base_salary: {
            type: Number,
            min: 0
        },
        currency: {
            type: String,
            default: 'USD'
        },
        pay_frequency: {
            type: String,
            enum: ['hourly', 'daily', 'weekly', 'monthly', 'yearly'],
            default: 'monthly'
        }
    },
    
    // Settings
    preferences: {
        notifications: {
            email: { type: Boolean, default: true },
            push: { type: Boolean, default: true },
            slack: { type: Boolean, default: true }
        },
        timezone: {
            type: String,
            default: 'UTC'
        },
        language: {
            type: String,
            default: 'en'
        }
    }
}, {
    timestamps: true, // Adds createdAt and updatedAt
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better performance
userSchema.index({ email: 1, organization_id: 1 });
userSchema.index({ role: 1, is_active: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for full display name
userSchema.virtual('display_name').get(function() {
    return this.full_name || this.email;
});

// Instance methods
userSchema.methods.toSafeObject = function() {
    const obj = this.toObject();
    delete obj.fcm_token;
    return obj;
};

userSchema.methods.updateLastLogin = function() {
    this.last_login = new Date();
    return this.save();
};

// Static methods
userSchema.statics.findByEmail = function(email) {
    return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByOrganization = function(organizationId) {
    return this.find({ organization_id: organizationId, is_active: true });
};

userSchema.statics.findByRole = function(role, organizationId = null) {
    const query = { role, is_active: true };
    if (organizationId) {
        query.organization_id = organizationId;
    }
    return this.find(query);
};

// Pre-save middleware
userSchema.pre('save', function(next) {
    if (this.isModified('email')) {
        this.email = this.email.toLowerCase();
    }
    if (this.isModified('personal_email') && this.personal_email) {
        this.personal_email = this.personal_email.toLowerCase();
    }
    next();
});

const User = mongoose.model('User', userSchema);

export default User;
