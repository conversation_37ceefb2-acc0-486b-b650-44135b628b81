import express from 'express';
import syncService from '../services/syncService.js';

const router = express.Router();

// Full sync from Supabase to MongoDB
router.post('/full-sync', async (req, res) => {
    try {
        console.log('🔄 Full sync requested via API');
        const result = await syncService.performFullSync();
        
        if (result.success) {
            res.json({
                success: true,
                message: 'Full sync completed successfully',
                data: result.results,
                timestamp: result.timestamp
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Full sync failed',
                error: result.error,
                timestamp: result.timestamp
            });
        }
    } catch (error) {
        console.error('❌ Sync API error:', error);
        res.status(500).json({
            success: false,
            message: 'Sync request failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Sync users only
router.post('/sync-users', async (req, res) => {
    try {
        console.log('🔄 User sync requested via API');
        const result = await syncService.syncUsersFromSupabase();
        
        res.json({
            success: true,
            message: 'User sync completed',
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ User sync API error:', error);
        res.status(500).json({
            success: false,
            message: 'User sync failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Sync attendance logs
router.post('/sync-attendance', async (req, res) => {
    try {
        const { start_date, end_date } = req.body;
        
        console.log('🔄 Attendance sync requested via API');
        const result = await syncService.syncAttendanceFromSupabase(start_date, end_date);
        
        res.json({
            success: true,
            message: 'Attendance sync completed',
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Attendance sync API error:', error);
        res.status(500).json({
            success: false,
            message: 'Attendance sync failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Sync projects only
router.post('/sync-projects', async (req, res) => {
    try {
        console.log('🔄 Project sync requested via API');
        const result = await syncService.syncProjectsFromSupabase();
        
        res.json({
            success: true,
            message: 'Project sync completed',
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Project sync API error:', error);
        res.status(500).json({
            success: false,
            message: 'Project sync failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Get sync status
router.get('/status', async (req, res) => {
    try {
        const mongoStatus = syncService.getMongoStatus ? syncService.getMongoStatus() : { isConnected: false };
        
        res.json({
            success: true,
            mongodb_available: syncService.isMongoAvailable,
            mongodb_status: mongoStatus,
            sync_service_ready: syncService.isMongoAvailable,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

export default router;
