import { createClient } from '@supabase/supabase-js';
import { User, AttendanceLog, Project } from '../models/index.js';
import { getMongoStatus } from '../config/mongodb.js';

const supabase = createClient(process.env.VITE_SUPABASE_URL, process.env.VITE_SUPABASE_ANON_KEY);

class SyncService {
    constructor() {
        this.isMongoAvailable = false;
        this.checkMongoAvailability();
    }

    checkMongoAvailability() {
        const status = getMongoStatus();
        this.isMongoAvailable = status.isConnected;
        console.log(`🔄 MongoDB sync service (PRIMARY DATABASE): ${this.isMongoAvailable ? 'enabled' : 'disabled'}`);
    }

    // ==================== USER SYNC ====================

    async syncUserToMongo(supabaseUser) {
        if (!this.isMongoAvailable) return null;

        try {
            const userData = {
                supabase_id: supabaseUser.id,
                full_name: supabaseUser.full_name,
                email: supabaseUser.email,
                personal_email: supabaseUser.personal_email,
                phone_number: supabaseUser.phone_number,
                role: supabaseUser.role || 'employee',
                organization_id: supabaseUser.organization_id,
                joining_date: supabaseUser.joining_date ? new Date(supabaseUser.joining_date) : new Date(),
                location: supabaseUser.location,
                slack_id: supabaseUser.slack_id,
                fcm_token: supabaseUser.fcm_token,
                client_metadata: supabaseUser.client_metadata || {},
                is_active: true
            };

            // Check if user already exists in MongoDB
            let mongoUser = await User.findOne({ supabase_id: supabaseUser.id });
            
            if (mongoUser) {
                // Update existing user
                Object.assign(mongoUser, userData);
                await mongoUser.save();
                console.log(`✅ Updated user in MongoDB: ${userData.email}`);
            } else {
                // Create new user
                mongoUser = new User(userData);
                await mongoUser.save();
                console.log(`✅ Created user in MongoDB: ${userData.email}`);
            }

            return mongoUser;
        } catch (error) {
            console.error('❌ Error syncing user to MongoDB:', error.message);
            return null;
        }
    }

    async syncUsersFromSupabase() {
        if (!this.isMongoAvailable) {
            console.log('⚠️ MongoDB not available, skipping user sync');
            return;
        }

        try {
            console.log('🔄 Starting user sync from Supabase to MongoDB...');

            const { data: users, error } = await supabase
                .from('users')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;

            let syncedCount = 0;
            let errorCount = 0;

            for (const user of users) {
                const result = await this.syncUserToMongo(user);
                if (result) {
                    syncedCount++;
                } else {
                    errorCount++;
                }
            }

            console.log(`✅ User sync completed: ${syncedCount} synced, ${errorCount} errors`);
            return { syncedCount, errorCount };
        } catch (error) {
            console.error('❌ Error syncing users from Supabase:', error.message);
            throw error;
        }
    }

    // ==================== ATTENDANCE SYNC ====================

    async syncAttendanceToMongo(supabaseLog) {
        if (!this.isMongoAvailable) return null;

        try {
            const attendanceData = {
                user_id: supabaseLog.user_id,
                supabase_user_id: supabaseLog.user_id,
                check_in: new Date(supabaseLog.check_in),
                check_out: supabaseLog.check_out ? new Date(supabaseLog.check_out) : null,
                break_start: supabaseLog.break_start ? new Date(supabaseLog.break_start) : null,
                break_end: supabaseLog.break_end ? new Date(supabaseLog.break_end) : null,
                work_mode: supabaseLog.work_mode || 'remote',
                status: supabaseLog.status || 'present',
                auto_checkout: supabaseLog.autocheckout || 'no',
                total_hours: supabaseLog.total_hours || 0,
                notes: supabaseLog.notes,
                ip_address: supabaseLog.ip_address
            };

            // Check if attendance log already exists
            let mongoLog = await AttendanceLog.findOne({
                user_id: supabaseLog.user_id,
                check_in: attendanceData.check_in
            });

            if (mongoLog) {
                // Update existing log
                Object.assign(mongoLog, attendanceData);
                await mongoLog.save();
            } else {
                // Create new log
                mongoLog = new AttendanceLog(attendanceData);
                await mongoLog.save();
            }

            return mongoLog;
        } catch (error) {
            console.error('❌ Error syncing attendance to MongoDB:', error.message);
            return null;
        }
    }

    async syncAttendanceFromSupabase(startDate = null, endDate = null) {
        if (!this.isMongoAvailable) {
            console.log('⚠️ MongoDB not available, skipping attendance sync');
            return;
        }

        try {
            console.log('🔄 Starting attendance sync from Supabase to MongoDB...');

            let query = supabase
                .from('attendance_logs')
                .select('*')
                .order('check_in', { ascending: false });

            if (startDate) {
                query = query.gte('check_in', startDate);
            }
            if (endDate) {
                query = query.lte('check_in', endDate);
            }

            const { data: logs, error } = await query;

            if (error) throw error;

            let syncedCount = 0;
            let errorCount = 0;

            for (const log of logs) {
                const result = await this.syncAttendanceToMongo(log);
                if (result) {
                    syncedCount++;
                } else {
                    errorCount++;
                }
            }

            console.log(`✅ Attendance sync completed: ${syncedCount} synced, ${errorCount} errors`);
            return { syncedCount, errorCount };
        } catch (error) {
            console.error('❌ Error syncing attendance from Supabase:', error.message);
            throw error;
        }
    }

    // ==================== PROJECT SYNC ====================

    async syncProjectToMongo(supabaseProject) {
        if (!this.isMongoAvailable) return null;

        try {
            const projectData = {
                supabase_id: supabaseProject.id,
                title: supabaseProject.title,
                description: supabaseProject.description,
                status: supabaseProject.status || 'planning',
                priority: supabaseProject.priority || 'medium',
                start_date: supabaseProject.start_date ? new Date(supabaseProject.start_date) : null,
                end_date: supabaseProject.end_date ? new Date(supabaseProject.end_date) : null,
                deadline: supabaseProject.deadline ? new Date(supabaseProject.deadline) : null,
                created_by: supabaseProject.created_by,
                product_owner: supabaseProject.product_owner,
                project_manager: supabaseProject.project_manager,
                devops: supabaseProject.devops || [],
                repository_url: supabaseProject.repository_url,
                deployment_url: supabaseProject.deployment_url,
                organization_id: supabaseProject.organization_id,
                custom_fields: supabaseProject.custom_fields || {}
            };

            // Check if project already exists
            let mongoProject = await Project.findOne({ supabase_id: supabaseProject.id });

            if (mongoProject) {
                // Update existing project
                Object.assign(mongoProject, projectData);
                await mongoProject.save();
            } else {
                // Create new project
                mongoProject = new Project(projectData);
                await mongoProject.save();
            }

            return mongoProject;
        } catch (error) {
            console.error('❌ Error syncing project to MongoDB:', error.message);
            return null;
        }
    }

    async syncProjectsFromSupabase() {
        if (!this.isMongoAvailable) {
            console.log('⚠️ MongoDB not available, skipping project sync');
            return;
        }

        try {
            console.log('🔄 Starting project sync from Supabase to MongoDB...');

            const { data: projects, error } = await supabase
                .from('projects')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;

            let syncedCount = 0;
            let errorCount = 0;

            for (const project of projects) {
                const result = await this.syncProjectToMongo(project);
                if (result) {
                    syncedCount++;
                } else {
                    errorCount++;
                }
            }

            console.log(`✅ Project sync completed: ${syncedCount} synced, ${errorCount} errors`);
            return { syncedCount, errorCount };
        } catch (error) {
            console.error('❌ Error syncing projects from Supabase:', error.message);
            throw error;
        }
    }

    // ==================== FULL SYNC ====================

    async performFullSync() {
        if (!this.isMongoAvailable) {
            console.log('⚠️ MongoDB not available, skipping full sync');
            return {
                success: false,
                message: 'MongoDB not available'
            };
        }

        try {
            console.log('🔄 Starting full data sync from Supabase to MongoDB...');

            const results = {
                users: await this.syncUsersFromSupabase(),
                attendance: await this.syncAttendanceFromSupabase(),
                projects: await this.syncProjectsFromSupabase()
            };

            console.log('✅ Full sync completed successfully');
            return {
                success: true,
                results,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Full sync failed:', error.message);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// Create singleton instance
const syncService = new SyncService();

export default syncService;
