import mongoose from 'mongoose';

const projectSchema = new mongoose.Schema({
    // Basic Information
    title: {
        type: String,
        required: true,
        trim: true,
        index: true
    },
    description: {
        type: String,
        trim: true
    },
    
    // Project Identification
    project_code: {
        type: String,
        unique: true,
        sparse: true,
        trim: true,
        uppercase: true
    },
    supabase_id: {
        type: String,
        unique: true,
        sparse: true,
        index: true
    },
    
    // Project Management
    status: {
        type: String,
        enum: ['planning', 'active', 'on_hold', 'completed', 'cancelled'],
        default: 'planning'
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
    },
    
    // Dates
    start_date: {
        type: Date,
        index: true
    },
    end_date: {
        type: Date,
        index: true
    },
    deadline: {
        type: Date,
        index: true
    },
    
    // Team and Ownership
    created_by: {
        type: String,
        required: true,
        index: true
    },
    product_owner: {
        type: String,
        index: true
    },
    project_manager: {
        type: String,
        index: true
    },
    
    // Team Members
    team_members: [{
        user_id: {
            type: String,
            required: true
        },
        role: {
            type: String,
            enum: ['developer', 'designer', 'tester', 'analyst', 'lead', 'other'],
            default: 'developer'
        },
        joined_date: {
            type: Date,
            default: Date.now
        },
        is_active: {
            type: Boolean,
            default: true
        }
    }],
    
    // DevOps and Technical
    devops: [{
        type: String
    }],
    repository_url: {
        type: String,
        trim: true
    },
    deployment_url: {
        type: String,
        trim: true
    },
    
    // Budget and Resources
    budget: {
        allocated: {
            type: Number,
            min: 0
        },
        spent: {
            type: Number,
            min: 0,
            default: 0
        },
        currency: {
            type: String,
            default: 'USD'
        }
    },
    
    // Progress Tracking
    progress: {
        percentage: {
            type: Number,
            min: 0,
            max: 100,
            default: 0
        },
        milestones_completed: {
            type: Number,
            min: 0,
            default: 0
        },
        total_milestones: {
            type: Number,
            min: 0,
            default: 0
        }
    },
    
    // Client Information
    client_info: {
        client_id: String,
        client_name: String,
        contact_person: String,
        contact_email: String,
        contact_phone: String
    },
    
    // Technology Stack
    technologies: [{
        name: {
            type: String,
            required: true
        },
        version: String,
        category: {
            type: String,
            enum: ['frontend', 'backend', 'database', 'devops', 'testing', 'other'],
            default: 'other'
        }
    }],
    
    // Project Settings
    settings: {
        is_public: {
            type: Boolean,
            default: false
        },
        allow_time_tracking: {
            type: Boolean,
            default: true
        },
        require_approval: {
            type: Boolean,
            default: false
        },
        notifications_enabled: {
            type: Boolean,
            default: true
        }
    },
    
    // Organization
    organization_id: {
        type: String,
        index: true
    },
    
    // Metadata
    tags: [{
        type: String,
        trim: true,
        lowercase: true
    }],
    custom_fields: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better performance
projectSchema.index({ status: 1, created_by: 1 });
projectSchema.index({ product_owner: 1, status: 1 });
projectSchema.index({ organization_id: 1, status: 1 });
projectSchema.index({ start_date: -1 });
projectSchema.index({ deadline: 1, status: 1 });

// Virtual for project duration
projectSchema.virtual('duration_days').get(function() {
    if (this.start_date && this.end_date) {
        const diffTime = Math.abs(this.end_date - this.start_date);
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    return null;
});

// Virtual for days remaining
projectSchema.virtual('days_remaining').get(function() {
    if (this.deadline) {
        const today = new Date();
        const diffTime = this.deadline - today;
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    return null;
});

// Virtual for active team members count
projectSchema.virtual('active_team_count').get(function() {
    return this.team_members.filter(member => member.is_active).length;
});

// Instance methods
projectSchema.methods.addTeamMember = function(userId, role = 'developer') {
    const existingMember = this.team_members.find(member => member.user_id === userId);
    if (!existingMember) {
        this.team_members.push({
            user_id: userId,
            role: role,
            joined_date: new Date(),
            is_active: true
        });
    } else {
        existingMember.is_active = true;
        existingMember.role = role;
    }
    return this.save();
};

projectSchema.methods.removeTeamMember = function(userId) {
    const member = this.team_members.find(member => member.user_id === userId);
    if (member) {
        member.is_active = false;
    }
    return this.save();
};

projectSchema.methods.updateProgress = function(percentage) {
    this.progress.percentage = Math.max(0, Math.min(100, percentage));
    if (this.progress.percentage === 100 && this.status === 'active') {
        this.status = 'completed';
    }
    return this.save();
};

// Static methods
projectSchema.statics.findByOwner = function(ownerId) {
    return this.find({
        $or: [
            { created_by: ownerId },
            { product_owner: ownerId },
            { project_manager: ownerId }
        ]
    }).sort({ createdAt: -1 });
};

projectSchema.statics.findByTeamMember = function(userId) {
    return this.find({
        'team_members.user_id': userId,
        'team_members.is_active': true
    }).sort({ createdAt: -1 });
};

projectSchema.statics.findActiveProjects = function(organizationId = null) {
    const query = { status: { $in: ['planning', 'active'] } };
    if (organizationId) {
        query.organization_id = organizationId;
    }
    return this.find(query).sort({ priority: -1, deadline: 1 });
};

// Pre-save middleware
projectSchema.pre('save', function(next) {
    // Generate project code if not provided
    if (!this.project_code && this.title) {
        const code = this.title.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 6);
        const timestamp = Date.now().toString().slice(-4);
        this.project_code = `${code}${timestamp}`;
    }
    
    // Update progress based on milestones
    if (this.progress.total_milestones > 0) {
        const calculatedProgress = (this.progress.milestones_completed / this.progress.total_milestones) * 100;
        this.progress.percentage = Math.round(calculatedProgress);
    }
    
    next();
});

const Project = mongoose.model('Project', projectSchema);

export default Project;
