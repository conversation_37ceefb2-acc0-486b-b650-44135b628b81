import express from 'express';
import nodemailer from 'nodemailer';

const router = express.Router();

// Email sending function for leave requests
router.post("/send-email", async (req, res) => {
    const { senderEmail, recipientEmail, subject, employeeName, leaveType, startDate, endDate, reason } = req.body;

    // Create transporter
    let transporter = nodemailer.createTransporter({
        service: "gmail",
        auth: {
            user: process.env.VITE_EMAIL_USER, // Your email (EMS system email)
            pass: process.env.VITE_EMAIL_PASS, // Your app password
        },
    });

    let message = `
    <p>Dear <strong>Admin</strong>,</p>

    <p>A new leave request has been submitted.</p>

    <table style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif;">
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;"><strong>Employee Name:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${employeeName}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;"><strong>Leave Type:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${leaveType}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;"><strong>Start Date:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${startDate}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;"><strong>End Date:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${endDate}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #ddd; padding: 8px;"><strong>Reason:</strong></td>
            <td style="border: 1px solid #ddd; padding: 8px;">${reason}</td>
        </tr>
    </table>

    <p>Please review and take necessary action.</p>

    <p>Best Regards, <br> <strong>TechCreator EMS System</strong></p>
    `;

    // Email options
    let mailOptions = {
        from: process.env.VITE_EMAIL_USER, // The email that actually sends the email
        to: recipientEmail, // Admin's email
        subject: subject,
        html: message,
        replyTo: senderEmail, // This ensures the admin's reply goes to the user
    };

    // Send email
    try {
        let info = await transporter.sendMail(mailOptions);
        console.log("Email sent: " + info.response);
        res.status(200).json({ message: "Email sent successfully!" });
    } catch (error) {
        console.error("Error sending email:", error);
        res.status(500).json({ error: "Failed to send email" });
    }
});

// Sending Bulk Email To Users On Office Alerts
router.post("/send-alertemail", async (req, res) => {
    const { recipients, subject, message } = req.body;

    if (!recipients || recipients.length === 0) {
        return res.status(400).json({ error: "Recipient list is empty" });
    }

    try {
        // Setup transporter
        const transporter = nodemailer.createTransporter({
            service: "gmail", // or another provider
            auth: {
                user: process.env.VITE_EMAIL_USER, // Your email (EMS system email)
                pass: process.env.VITE_EMAIL_PASS, // Your app password
            },
        });

        // Send email
        const info = await transporter.sendMail({
            from: process.env.VITE_EMAIL_USER, // The email that actually sends the email
            to: "", // empty TO
            bcc: recipients, // list of emails
            subject,
            text: message, // or use html: "<b>Hello</b>"
        });

        console.log("Message sent: %s", info.messageId);
        res.json({ status: "Emails sent successfully" });
    } catch (error) {
        console.error("Error sending email:", error);
        res.status(500).json({ error: "Failed to send emails", detail: error.message });
    }
});

// Send approval response
router.post("/send-response", async (req, res) => {
    const { employeeName, userEmail, leaveType, startDate } = req.body;

    let transporter = nodemailer.createTransporter({
        service: "gmail",
        auth: {
            user: process.env.VITE_EMAIL_USER, // EMS system email
            pass: process.env.VITE_EMAIL_PASS, // App password
        },
    });

    let message = `
    <p>Dear <strong>${employeeName}</strong>,</p>

    <p>Your leave request has been <strong style="color: green;">Approved</strong>.</p>

    <p><strong>Leave Details:</strong></p>
    <ul>
        <li><strong>Leave Type:</strong> ${leaveType}</li>
        <li><strong>Start Date:</strong> ${startDate}</li>
        <li><strong>End Date:</strong> ${startDate}</li>
    </ul>

    <p>Enjoy your time off, and please reach out if you have any questions.</p>

    <p>Best Regards, <br> <strong>TechCreator HR Team</strong></p>
    `;

    let mailOptions = {
        from: process.env.VITE_EMAIL_USER,
        to: userEmail,
        subject: "Leave Request Approved",
        html: message, // Using HTML format for better styling
        replyTo: "<EMAIL>",
    };

    try {
        let info = await transporter.sendMail(mailOptions);
        console.log("Response Email sent: " + info.response);
        res.status(200).json({ message: "Response email sent successfully!" });
    } catch (error) {
        console.error("Error sending response email:", error);
        res.status(500).json({ error: "Failed to send response email" });
    }
});

// Send rejection response
router.post("/send-rejectresponse", async (req, res) => {
    const { employeeName, userEmail, leaveType, startDate } = req.body;

    let transporter = nodemailer.createTransporter({
        service: "gmail",
        auth: {
            user: process.env.VITE_EMAIL_USER, // EMS system email
            pass: process.env.VITE_EMAIL_PASS, // App password
        },
    });

    let message = `
    <p>Dear <strong>${employeeName}</strong>,</p>

    <p>We regret to inform you that your leave request has been <strong style="color: red;">rejected</strong>.</p>

    <p><strong>Leave Details:</strong></p>
    <ul>
        <li><strong>Leave Type:</strong> ${leaveType}</li>
        <li><strong>Start Date:</strong> ${startDate}</li>
        <li><strong>End Date:</strong> ${startDate}</li>
    </ul>

    <p>If you have any concerns, please contact HR.</p>

    <p>Best Regards, <br> <strong>TechCreator HR Team</strong></p>
    `;

    let mailOptions = {
        from: process.env.VITE_EMAIL_USER,
        to: userEmail,
        subject: "Leave Request Rejected",
        html: message, // Send as HTML email
        replyTo: "<EMAIL>",
    };

    try {
        let info = await transporter.sendMail(mailOptions);
        console.log("Rejection Email sent: " + info.response);
        res.status(200).json({ message: "Rejection email sent successfully!" });
    } catch (error) {
        console.error("Error sending rejection email:", error);
        res.status(500).json({ error: "Failed to send rejection email" });
    }
});

// Send task assignment email
router.post("/sendtaskemail", async (req, res) => {
    const { username, projectName, kpiCount, projectId, priority, recipientEmail } = req.body;

    if (!recipientEmail || !username) {
        return res.status(400).json({ error: "Recipient email and username are required" });
    }

    try {
        // Create transporter
        const transporter = nodemailer.createTransporter({
            service: "gmail",
            auth: {
                user: process.env.VITE_EMAIL_USER,
                pass: process.env.VITE_EMAIL_PASS,
            },
        });

        const message = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">New Task Assignment</h2>

            <p>Dear <strong>${username}</strong>,</p>

            <p>You have been assigned a new task in the EMS system.</p>

            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #555;">Task Details:</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Project:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${projectName}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Priority:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${priority}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>KPI Score:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${kpiCount}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px;"><strong>Project ID:</strong></td>
                        <td style="padding: 8px;">${projectId}</td>
                    </tr>
                </table>
            </div>

            <p>Please log into the EMS system to view the complete task details and start working on it.</p>

            <p style="margin-top: 30px;">
                Best regards,<br>
                <strong>TechCreator EMS Team</strong>
            </p>
        </div>
        `;

        const mailOptions = {
            from: process.env.VITE_EMAIL_USER,
            to: recipientEmail,
            subject: `New Task Assignment - ${projectName}`,
            html: message,
        };

        const info = await transporter.sendMail(mailOptions);
        console.log("Task email sent: " + info.response);
        res.status(200).json({ message: "Task email sent successfully!" });
    } catch (error) {
        console.error("Error sending task email:", error);
        res.status(500).json({ error: "Failed to send task email", detail: error.message });
    }
});

// Client invitation endpoint
router.post("/inviteClient", async (req, res) => {
    const { email, personalEmail, password } = req.body;

    if (!email || !personalEmail) {
        return res.status(400).json({ error: "Email and personal email are required" });
    }

    try {
        // Create transporter
        const transporter = nodemailer.createTransporter({
            service: "gmail",
            auth: {
                user: process.env.VITE_EMAIL_USER,
                pass: process.env.VITE_EMAIL_PASS,
            },
        });

        const message = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Welcome to TechCreator EMS</h2>

            <p>Dear Client,</p>

            <p>You have been invited to join the TechCreator Employee Management System.</p>

            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #555;">Your Account Details:</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Email:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${email}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;"><strong>Personal Email:</strong></td>
                        <td style="padding: 8px; border-bottom: 1px solid #ddd;">${personalEmail}</td>
                    </tr>
                    ${password ? `
                    <tr>
                        <td style="padding: 8px;"><strong>Temporary Password:</strong></td>
                        <td style="padding: 8px;">${password}</td>
                    </tr>
                    ` : ''}
                </table>
            </div>

            <p>Please log into the EMS system using your credentials. You will be prompted to change your password on first login.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="https://ems-one-mauve.vercel.app/login"
                   style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
                   Access EMS System
                </a>
            </div>

            <p style="margin-top: 30px;">
                Best regards,<br>
                <strong>TechCreator Team</strong>
            </p>
        </div>
        `;

        const mailOptions = {
            from: process.env.VITE_EMAIL_USER,
            to: personalEmail,
            cc: email,
            subject: "Welcome to TechCreator EMS - Client Invitation",
            html: message,
        };

        const info = await transporter.sendMail(mailOptions);
        console.log("Client invitation email sent: " + info.response);
        res.status(200).json({ message: "Client invitation email sent successfully!" });
    } catch (error) {
        console.error("Error sending client invitation email:", error);
        res.status(500).json({ error: "Failed to send client invitation email", detail: error.message });
    }
});

export default router;
