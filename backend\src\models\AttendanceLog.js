import mongoose from 'mongoose';

const attendanceLogSchema = new mongoose.Schema({
    // User Information
    user_id: {
        type: String,
        required: true,
        index: true
    },
    supabase_user_id: {
        type: String,
        index: true
    },
    
    // Attendance Times
    check_in: {
        type: Date,
        required: true,
        index: true
    },
    check_out: {
        type: Date,
        index: true
    },
    
    // Break Times
    break_start: {
        type: Date
    },
    break_end: {
        type: Date
    },
    
    // Work Information
    work_mode: {
        type: String,
        enum: ['remote', 'on_site', 'hybrid'],
        default: 'remote'
    },
    
    // Location Information
    check_in_location: {
        latitude: Number,
        longitude: Number,
        address: String
    },
    check_out_location: {
        latitude: Number,
        longitude: Number,
        address: String
    },
    
    // Status and Flags
    status: {
        type: String,
        enum: ['present', 'absent', 'late', 'early_leave', 'half_day'],
        default: 'present'
    },
    is_late: {
        type: Boolean,
        default: false
    },
    is_early_leave: {
        type: Boolean,
        default: false
    },
    auto_checkout: {
        type: String,
        enum: ['yes', 'no'],
        default: 'no'
    },
    
    // Time Calculations
    total_hours: {
        type: Number,
        min: 0,
        default: 0
    },
    break_duration: {
        type: Number,
        min: 0,
        default: 0
    },
    overtime_hours: {
        type: Number,
        min: 0,
        default: 0
    },
    
    // Notes and Comments
    notes: {
        type: String,
        trim: true
    },
    admin_notes: {
        type: String,
        trim: true
    },
    
    // Approval
    approved_by: {
        type: String
    },
    approval_date: {
        type: Date
    },
    
    // Metadata
    ip_address: {
        type: String
    },
    user_agent: {
        type: String
    },
    device_info: {
        type: mongoose.Schema.Types.Mixed
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better performance
attendanceLogSchema.index({ user_id: 1, check_in: -1 });
attendanceLogSchema.index({ check_in: 1 });
attendanceLogSchema.index({ status: 1, check_in: -1 });
attendanceLogSchema.index({ work_mode: 1, check_in: -1 });

// Virtual for date only (without time)
attendanceLogSchema.virtual('date').get(function() {
    return this.check_in ? this.check_in.toISOString().split('T')[0] : null;
});

// Virtual for work duration in hours
attendanceLogSchema.virtual('work_duration').get(function() {
    if (this.check_in && this.check_out) {
        const duration = (this.check_out - this.check_in) / (1000 * 60 * 60); // Convert to hours
        return Math.round(duration * 100) / 100; // Round to 2 decimal places
    }
    return 0;
});

// Instance methods
attendanceLogSchema.methods.calculateTotalHours = function() {
    if (this.check_in && this.check_out) {
        const totalMs = this.check_out - this.check_in;
        const breakMs = this.break_duration * 60 * 1000; // Convert minutes to milliseconds
        const workMs = totalMs - breakMs;
        this.total_hours = Math.max(0, workMs / (1000 * 60 * 60)); // Convert to hours
    }
    return this.total_hours;
};

attendanceLogSchema.methods.checkOut = function(checkOutTime = new Date(), location = null) {
    this.check_out = checkOutTime;
    if (location) {
        this.check_out_location = location;
    }
    this.calculateTotalHours();
    return this.save();
};

attendanceLogSchema.methods.startBreak = function(breakTime = new Date()) {
    this.break_start = breakTime;
    return this.save();
};

attendanceLogSchema.methods.endBreak = function(breakEndTime = new Date()) {
    if (this.break_start) {
        this.break_end = breakEndTime;
        const breakDuration = (breakEndTime - this.break_start) / (1000 * 60); // Convert to minutes
        this.break_duration = (this.break_duration || 0) + breakDuration;
        this.calculateTotalHours();
    }
    return this.save();
};

// Static methods
attendanceLogSchema.statics.findByUser = function(userId, startDate = null, endDate = null) {
    const query = { user_id: userId };
    if (startDate || endDate) {
        query.check_in = {};
        if (startDate) query.check_in.$gte = new Date(startDate);
        if (endDate) query.check_in.$lte = new Date(endDate);
    }
    return this.find(query).sort({ check_in: -1 });
};

attendanceLogSchema.statics.findByDate = function(date) {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    return this.find({
        check_in: {
            $gte: startOfDay,
            $lte: endOfDay
        }
    });
};

attendanceLogSchema.statics.getTodayAttendance = function(userId) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    return this.findOne({
        user_id: userId,
        check_in: {
            $gte: today,
            $lt: tomorrow
        }
    });
};

// Pre-save middleware
attendanceLogSchema.pre('save', function(next) {
    // Calculate total hours if check_in and check_out are present
    if (this.isModified('check_out') || this.isModified('break_duration')) {
        this.calculateTotalHours();
    }
    
    // Determine if late or early leave based on business rules
    // This can be customized based on company policies
    if (this.check_in) {
        const checkInHour = this.check_in.getHours();
        if (checkInHour > 9) { // Assuming 9 AM is the standard start time
            this.is_late = true;
            this.status = 'late';
        }
    }
    
    next();
});

const AttendanceLog = mongoose.model('AttendanceLog', attendanceLogSchema);

export default AttendanceLog;
