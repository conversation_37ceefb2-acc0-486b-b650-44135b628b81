@echo off
REM Installation script for EMS project dependencies (Windows)

echo 🚀 Installing EMS Project Dependencies
echo ======================================

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ npm is not installed. Please install Node.js and npm first.
    pause
    exit /b 1
)

echo 📦 Installing Backend Dependencies...
cd backend

if not exist "package.json" (
    echo ❌ Backend package.json not found!
    pause
    exit /b 1
)

REM Install backend dependencies
call npm install

if %ERRORLEVEL% EQU 0 (
    echo ✅ Backend dependencies installed successfully
) else (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

REM Go back to root and install frontend dependencies
cd ..
echo 📦 Installing Frontend Dependencies...
cd frontend

if not exist "package.json" (
    echo ❌ Frontend package.json not found!
    pause
    exit /b 1
)

REM Install frontend dependencies
call npm install

if %ERRORLEVEL% EQU 0 (
    echo ✅ Frontend dependencies installed successfully
) else (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)

cd ..

echo.
echo 🎉 All dependencies installed successfully!
echo.
echo 📋 Next Steps:
echo 1. Copy backend\.env.example to backend\.env and configure it
echo 2. Copy frontend\.env.example to frontend\.env and configure it (if needed)
echo 3. Run start-dev.bat to start both servers
echo.
echo 📍 Default URLs:
echo    Frontend: http://localhost:5173
echo    Backend:  http://localhost:5000
echo    Health:   http://localhost:5000/health
echo.
pause
