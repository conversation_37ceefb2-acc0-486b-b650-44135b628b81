@echo off
REM Development startup script for EMS project (Windows)
REM This script starts both frontend and backend in development mode

echo 🚀 Starting EMS Development Environment
echo =======================================

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ npm is not installed. Please install Node.js and npm first.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "frontend" (
    if not exist "backend" (
        echo ❌ Please run this script from the project root directory
        pause
        exit /b 1
    )
)

REM Start backend
echo 📡 Starting backend server...
cd backend

if not exist "node_modules" (
    echo 📦 Installing backend dependencies...
    call npm install
)

REM Check if .env exists
if not exist ".env" (
    echo ⚠️  Backend .env file not found. Please copy .env.example to .env and configure it.
    echo    copy .env.example .env
)

REM Start backend in new window
start "EMS Backend" cmd /k "npm run dev"
echo ✅ Backend started in new window

REM Go back to root and start frontend
cd ..
echo 🎨 Starting frontend...
cd frontend

if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    call npm install
)

REM Start frontend in new window
start "EMS Frontend" cmd /k "npm run dev"
echo ✅ Frontend started in new window

echo.
echo 🎉 Development environment is running!
echo 📍 Frontend: http://localhost:5173
echo 📍 Backend:  http://localhost:5000
echo 📍 Backend Health: http://localhost:5000/health
echo.
echo Both servers are running in separate windows.
echo Close the command windows to stop the servers.
echo.
pause
