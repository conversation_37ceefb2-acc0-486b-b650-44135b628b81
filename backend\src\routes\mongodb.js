import express from 'express';
import { User, AttendanceLog, Project } from '../models/index.js';
import { getMongoStatus } from '../config/mongodb.js';

const router = express.Router();

// MongoDB Health Check
router.get('/health', async (req, res) => {
    try {
        const status = getMongoStatus();
        res.json({
            success: true,
            mongodb: status,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// ==================== USER ROUTES ====================

// Get all users
router.get('/users', async (req, res) => {
    try {
        const { organization_id, role, page = 1, limit = 50 } = req.query;
        
        const query = { is_active: true };
        if (organization_id) query.organization_id = organization_id;
        if (role) query.role = role;
        
        const skip = (page - 1) * limit;
        
        const users = await User.find(query)
            .select('-fcm_token') // Exclude sensitive data
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));
            
        const total = await User.countDocuments(query);
        
        res.json({
            success: true,
            data: users,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Get user by ID
router.get('/users/:id', async (req, res) => {
    try {
        const user = await User.findById(req.params.id).select('-fcm_token');
        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }
        
        res.json({
            success: true,
            data: user
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Create new user
router.post('/users', async (req, res) => {
    try {
        const userData = req.body;
        
        // Check if user already exists
        const existingUser = await User.findByEmail(userData.email);
        if (existingUser) {
            return res.status(400).json({
                success: false,
                error: 'User with this email already exists'
            });
        }
        
        const user = new User(userData);
        await user.save();
        
        res.status(201).json({
            success: true,
            data: user.toSafeObject(),
            message: 'User created successfully'
        });
    } catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Update user
router.put('/users/:id', async (req, res) => {
    try {
        const user = await User.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        ).select('-fcm_token');
        
        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }
        
        res.json({
            success: true,
            data: user,
            message: 'User updated successfully'
        });
    } catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Soft delete user (deactivate)
router.delete('/users/:id', async (req, res) => {
    try {
        const user = await User.findByIdAndUpdate(
            req.params.id,
            { is_active: false },
            { new: true }
        ).select('-fcm_token');
        
        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }
        
        res.json({
            success: true,
            data: user,
            message: 'User deactivated successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// ==================== ATTENDANCE ROUTES ====================

// Get attendance logs
router.get('/attendance', async (req, res) => {
    try {
        const { user_id, date, start_date, end_date, page = 1, limit = 50 } = req.query;
        
        const query = {};
        if (user_id) query.user_id = user_id;
        
        if (date) {
            const logs = await AttendanceLog.findByDate(date);
            return res.json({
                success: true,
                data: logs
            });
        }
        
        if (start_date || end_date) {
            query.check_in = {};
            if (start_date) query.check_in.$gte = new Date(start_date);
            if (end_date) query.check_in.$lte = new Date(end_date);
        }
        
        const skip = (page - 1) * limit;
        
        const logs = await AttendanceLog.find(query)
            .sort({ check_in: -1 })
            .skip(skip)
            .limit(parseInt(limit));
            
        const total = await AttendanceLog.countDocuments(query);
        
        res.json({
            success: true,
            data: logs,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Create attendance log (check-in)
router.post('/attendance', async (req, res) => {
    try {
        const attendanceData = req.body;
        
        // Check if user already has attendance for today
        const existingLog = await AttendanceLog.getTodayAttendance(attendanceData.user_id);
        if (existingLog) {
            return res.status(400).json({
                success: false,
                error: 'User already has attendance record for today'
            });
        }
        
        const log = new AttendanceLog(attendanceData);
        await log.save();
        
        res.status(201).json({
            success: true,
            data: log,
            message: 'Check-in recorded successfully'
        });
    } catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// Update attendance log (check-out, break, etc.)
router.put('/attendance/:id', async (req, res) => {
    try {
        const log = await AttendanceLog.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        );
        
        if (!log) {
            return res.status(404).json({
                success: false,
                error: 'Attendance log not found'
            });
        }
        
        res.json({
            success: true,
            data: log,
            message: 'Attendance updated successfully'
        });
    } catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

// ==================== PROJECT ROUTES ====================

// Get all projects
router.get('/projects', async (req, res) => {
    try {
        const { organization_id, status, owner_id, team_member_id, page = 1, limit = 50 } = req.query;
        
        let query = {};
        if (organization_id) query.organization_id = organization_id;
        if (status) query.status = status;
        
        if (owner_id) {
            query.$or = [
                { created_by: owner_id },
                { product_owner: owner_id },
                { project_manager: owner_id }
            ];
        }
        
        if (team_member_id) {
            query['team_members.user_id'] = team_member_id;
            query['team_members.is_active'] = true;
        }
        
        const skip = (page - 1) * limit;
        
        const projects = await Project.find(query)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));
            
        const total = await Project.countDocuments(query);
        
        res.json({
            success: true,
            data: projects,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Create new project
router.post('/projects', async (req, res) => {
    try {
        const project = new Project(req.body);
        await project.save();
        
        res.status(201).json({
            success: true,
            data: project,
            message: 'Project created successfully'
        });
    } catch (error) {
        res.status(400).json({
            success: false,
            error: error.message
        });
    }
});

export default router;
