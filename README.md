# Employee Management System (EMS)

A comprehensive Employee Management System built with React (frontend) and Express.js (backend).

## 🏗️ Project Structure

```
EMS/
├── frontend/          # React frontend application
│   ├── src/          # Source code
│   ├── public/       # Static assets
│   └── package.json  # Frontend dependencies
├── backend/          # Express.js backend API
│   ├── src/          # Source code
│   │   ├── routes/   # API routes
│   │   ├── config/   # Configuration files
│   │   ├── utils/    # Utility functions
│   │   └── server.js # Main server file
│   └── package.json  # Backend dependencies
├── start-dev.sh      # Development startup script (Linux/Mac)
├── start-dev.bat     # Development startup script (Windows)
└── README.md         # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Supabase account and project

### Installation

#### Option 1: Automated Installation (Recommended)

**Linux/Mac:**
```bash
chmod +x install-dependencies.sh
./install-dependencies.sh
```

**Windows:**
```cmd
install-dependencies.bat
```

#### Option 2: Manual Installation

1. **Install Backend Dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Install Frontend Dependencies**
   ```bash
   cd frontend
   npm install
   ```

### Development Setup

#### Option 1: Use Startup Scripts (Recommended)

**Linux/Mac:**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

**Windows:**
```cmd
start-dev.bat
```

#### Option 2: Manual Setup

1. **Backend Setup**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your configuration
   npm run dev
   ```

2. **Frontend Setup** (in a new terminal)
   ```bash
   cd frontend
   npm run dev
   ```

### Environment Configuration

#### Backend (.env)
```env
PORT=5000
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_SLACK_BOT_USER_OAUTH_TOKEN=your_slack_token
VITE_EMAIL_USER=your_email
VITE_EMAIL_PASS=your_email_password
```

#### Frontend (.env)
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_BACKEND_URL=http://localhost:5000
```

## 📡 API Endpoints

### Health Check
- `GET /health` - Server status

### Notifications
- `POST /api/notifications/send-notifications` - Send to all users
- `POST /api/notifications/send-singlenotifications` - Send to specific user

### Slack Integration
- `POST /api/slack/send-slack` - Send approval notification
- `POST /api/slack/send-slackreject` - Send rejection notification
- `POST /api/slack/send-dailylog-slack` - Send daily log
- `POST /api/slack/get-slack-messages` - Get user messages

## 🛠️ Development

### Frontend Development
```bash
cd frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
```

### Backend Development
```bash
cd backend
npm run dev          # Start with nodemon
npm start           # Start production server
npm run fetch-users # Run user fetch utility
```

## 🔧 Features

- **Employee Management**: User profiles, roles, and permissions
- **Attendance Tracking**: Check-in/out, break management
- **Task Management**: Project and task assignment
- **Leave Management**: Request and approval system
- **Notifications**: Push notifications and Slack integration
- **Reports**: PDF generation for attendance and reports
- **Real-time Updates**: Live data synchronization

## 🏃‍♂️ Production Deployment

### Backend Deployment
1. Set environment variables on your hosting platform
2. Install dependencies: `npm install`
3. Start server: `npm start`

### Frontend Deployment
1. Build the application: `npm run build`
2. Deploy the `dist` folder to your hosting platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the ISC License.

## 🔧 Troubleshooting

### Common Issues

#### "Cannot find package 'firebase-admin'" Error
```bash
cd backend
npm install
```

#### "Cannot find package" Errors
Run the installation script:
```bash
# Linux/Mac
./install-dependencies.sh

# Windows
install-dependencies.bat
```

#### Backend Won't Start
1. Check if dependencies are installed: `cd backend && npm list`
2. Check environment variables: Copy `.env.example` to `.env`
3. Check Node.js version: `node --version` (should be v18+)

#### Frontend Won't Start
1. Check if dependencies are installed: `cd frontend && npm list`
2. Check if backend is running on port 5000
3. Clear cache: `npm run build` then `npm run dev`

#### Port Already in Use
- Backend (5000): Change `PORT` in backend/.env
- Frontend (5173): Change port in frontend/vite.config.ts

## 🆘 Support

For support and questions:
- Check the documentation in each directory
- Review the API endpoints in backend/README.md
- Check environment configuration examples
- Run installation scripts if dependencies are missing
